from funasr import AutoModel
# paraformer-zh is a multi-functional asr model
# use vad, punc, spk or not as you need


asr_modeldir = "E:\\work\\asr\\sichuan_asr_model\\sichuan_asr_model"
vad_modeldir = "E:\\work\\asr\\sichuanghua\\speech_fsmn_vad_zh-cn-16k-common-pytorch"
punc_modeldir = "E:\\work\\asr\\sichuanghua\\punc_ct-transformer_zh-cn-common-vocab272727-pytorch"
        # model = AutoModel(model=model_dir,vad_model=vad_modeldir, punc_model=punc_modeldir)


model = AutoModel(model=asr_modeldir,
                  vad_model=vad_modeldir, 
                  punc_model=punc_modeldir,
                  disable_update=True,
                  # spk_model="cam++"
                  batch_size_s=300,
                  hotword='魔搭'
                  )
res = model.generate(input="E:\\work\\asr\\speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-online\\example\\asr_example.wav")
print(res)